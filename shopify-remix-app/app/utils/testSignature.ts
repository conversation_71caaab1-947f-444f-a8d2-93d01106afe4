/**
 * Test utility for Shopify signature verification
 * This file helps you test and debug signature verification
 */

import { validateShopifyProxyRequest, verifyShopifySignature } from './shopifySignature';
import crypto from 'crypto';

/**
 * Generate a test signature for given parameters
 * Useful for testing and development
 */
export function generateTestSignature(
  params: Record<string, string>,
  sharedSecret: string
): string {
  // Remove signature if present
  const { signature, ...otherParams } = params;
  
  // Sort parameters alphabetically by key and create query string
  const sortedParams = Object.keys(otherParams)
    .sort()
    .map(key => `${key}=${otherParams[key]}`)
    .join('');

  // Calculate HMAC
  return crypto
    .createHmac('sha256', sharedSecret)
    .update(sortedParams)
    .digest('hex');
}

/**
 * Create test parameters that would come from Shopify
 */
export function createTestShopifyParams(options: {
  shop?: string;
  customerId?: string;
  pathPrefix?: string;
  timestamp?: string;
  sharedSecret: string;
}) {
  const {
    shop = 'test-shop.myshopify.com',
    customerId = '123456',
    pathPrefix = '/apps/dashboard',
    timestamp = Math.floor(Date.now() / 1000).toString(),
    sharedSecret
  } = options;

  const params = {
    shop,
    logged_in_customer_id: customerId,
    path_prefix: pathPrefix,
    timestamp
  };

  const signature = generateTestSignature(params, sharedSecret);

  return {
    ...params,
    signature
  };
}

/**
 * Test signature verification with sample data
 */
export function testSignatureVerification() {
  const sharedSecret = 'test-secret-key';
  
  console.log('🧪 Testing Shopify Signature Verification\n');

  // Test 1: Valid signature
  console.log('Test 1: Valid signature');
  const validParams = createTestShopifyParams({ sharedSecret });
  console.log('Parameters:', validParams);
  
  const validation1 = validateShopifyProxyRequest(validParams, sharedSecret);
  console.log('Validation result:', validation1);
  console.log('✅ Should be valid:', validation1.isValid);
  console.log('');

  // Test 2: Invalid signature
  console.log('Test 2: Invalid signature');
  const invalidParams = { ...validParams, signature: 'invalid-signature' };
  const validation2 = validateShopifyProxyRequest(invalidParams, sharedSecret);
  console.log('Validation result:', validation2);
  console.log('❌ Should be invalid:', !validation2.isValid);
  console.log('');

  // Test 3: Expired timestamp
  console.log('Test 3: Expired timestamp');
  const expiredParams = createTestShopifyParams({
    sharedSecret,
    timestamp: Math.floor((Date.now() - 10 * 60 * 1000) / 1000).toString() // 10 minutes ago
  });
  const validation3 = validateShopifyProxyRequest(expiredParams, sharedSecret, {
    validateTimestamp: true,
    maxAgeSeconds: 300 // 5 minutes
  });
  console.log('Validation result:', validation3);
  console.log('❌ Should be invalid (expired):', !validation3.isValid);
  console.log('');

  // Test 4: Missing customer ID
  console.log('Test 4: Missing customer ID');
  const noCustomerParams = createTestShopifyParams({
    sharedSecret,
    customerId: ''
  });
  const validation4 = validateShopifyProxyRequest(noCustomerParams, sharedSecret, {
    requireCustomerId: true
  });
  console.log('Validation result:', validation4);
  console.log('❌ Should be invalid (no customer):', !validation4.isValid);
  console.log('');

  return {
    test1: validation1.isValid,
    test2: !validation2.isValid,
    test3: !validation3.isValid,
    test4: !validation4.isValid
  };
}

/**
 * Verify a real signature from Shopify request
 */
export function verifyRealSignature(
  queryString: string,
  sharedSecret: string
) {
  console.log('🔍 Verifying real Shopify signature\n');
  
  // Parse query string
  const params = new URLSearchParams(queryString);
  const paramObj: Record<string, string> = {};
  
  for (const [key, value] of params.entries()) {
    paramObj[key] = value;
  }
  
  console.log('Parameters:', paramObj);
  
  const validation = validateShopifyProxyRequest(paramObj, sharedSecret);
  console.log('Validation result:', validation);
  
  if (validation.isValid) {
    console.log('✅ Signature is valid!');
  } else {
    console.log('❌ Signature is invalid!');
    console.log('Errors:', validation.errors);
  }
  
  return validation;
}

/**
 * Debug signature calculation step by step
 */
export function debugSignatureCalculation(
  params: Record<string, string>,
  sharedSecret: string
) {
  console.log('🔧 Debug signature calculation\n');
  
  const { signature, ...otherParams } = params;
  
  console.log('1. Original parameters:', params);
  console.log('2. Parameters without signature:', otherParams);
  
  const sortedKeys = Object.keys(otherParams).sort();
  console.log('3. Sorted keys:', sortedKeys);
  
  const keyValuePairs = sortedKeys.map(key => `${key}=${otherParams[key]}`);
  console.log('4. Key-value pairs:', keyValuePairs);
  
  const concatenated = keyValuePairs.join('');
  console.log('5. Concatenated string:', concatenated);
  
  const calculatedSignature = crypto
    .createHmac('sha256', sharedSecret)
    .update(concatenated)
    .digest('hex');
  
  console.log('6. Calculated signature:', calculatedSignature);
  console.log('7. Provided signature:', signature);
  console.log('8. Signatures match:', calculatedSignature === signature);
  
  return {
    concatenatedString: concatenated,
    calculatedSignature,
    providedSignature: signature,
    isValid: calculatedSignature === signature
  };
}

// Export for use in development/testing
if (typeof window === 'undefined') {
  // Only run in Node.js environment (server-side)
  // Uncomment to run tests:
  // testSignatureVerification();
}
