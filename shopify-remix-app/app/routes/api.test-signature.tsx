import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { validateShopifyProxyRequest } from "../utils/shopifySignature";
import { createTestShopifyParams, debugSignatureCalculation } from "../utils/testSignature";

/**
 * API endpoint to test Shopify signature verification
 * Usage: GET /api/test-signature?mode=test|debug&[other params]
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const mode = url.searchParams.get('mode') || 'test';
  const sharedSecret = process.env.SHOPIFY_SHARED_SECRET;

  if (!sharedSecret) {
    return json({
      error: 'SHOPIFY_SHARED_SECRET not configured',
      success: false
    }, { status: 500 });
  }

  try {
    if (mode === 'test') {
      // Generate test parameters and verify them
      const testParams = createTestShopifyParams({ sharedSecret });
      const validation = validateShopifyProxyRequest(testParams, sharedSecret);
      
      return json({
        success: true,
        mode: 'test',
        testParams,
        validation,
        message: validation.isValid ? 'Test signature verification passed!' : 'Test failed'
      });
    }
    
    if (mode === 'debug') {
      // Debug real parameters from query string
      const queryParams = Object.fromEntries(url.searchParams.entries());
      delete queryParams.mode; // Remove mode parameter
      
      if (Object.keys(queryParams).length === 0) {
        return json({
          error: 'No parameters provided for debugging. Add query parameters like: ?mode=debug&shop=test.myshopify.com&logged_in_customer_id=123&timestamp=1234567890&signature=abc123',
          success: false
        });
      }
      
      const validation = validateShopifyProxyRequest(queryParams, sharedSecret);
      const debug = debugSignatureCalculation(queryParams, sharedSecret);
      
      return json({
        success: true,
        mode: 'debug',
        queryParams,
        validation,
        debug,
        message: validation.isValid ? 'Signature is valid!' : 'Signature is invalid!'
      });
    }
    
    if (mode === 'verify') {
      // Verify current request parameters
      const queryParams = Object.fromEntries(url.searchParams.entries());
      delete queryParams.mode; // Remove mode parameter
      
      const validation = validateShopifyProxyRequest(queryParams, sharedSecret, {
        validateTimestamp: true,
        maxAgeSeconds: 300,
        requireCustomerId: false
      });
      
      return json({
        success: true,
        mode: 'verify',
        queryParams,
        validation,
        timestamp: {
          current: Math.floor(Date.now() / 1000),
          provided: queryParams.timestamp,
          age: queryParams.timestamp ? Math.floor(Date.now() / 1000) - parseInt(queryParams.timestamp) : null
        },
        message: validation.isValid ? 'Request signature is valid!' : 'Request signature is invalid!'
      });
    }
    
    return json({
      error: 'Invalid mode. Use: test, debug, or verify',
      availableModes: {
        test: 'Generate and verify test parameters',
        debug: 'Debug provided parameters step by step',
        verify: 'Verify current request parameters'
      },
      examples: {
        test: '/api/test-signature?mode=test',
        debug: '/api/test-signature?mode=debug&shop=test.myshopify.com&logged_in_customer_id=123&timestamp=1234567890&signature=abc123',
        verify: '/api/test-signature?mode=verify&shop=test.myshopify.com&logged_in_customer_id=123&timestamp=1234567890&signature=abc123'
      },
      success: false
    });
    
  } catch (error) {
    return json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false
    }, { status: 500 });
  }
};

// Also handle POST requests for testing
export const action = async ({ request }: LoaderFunctionArgs) => {
  const sharedSecret = process.env.SHOPIFY_SHARED_SECRET;

  if (!sharedSecret) {
    return json({
      error: 'SHOPIFY_SHARED_SECRET not configured',
      success: false
    }, { status: 500 });
  }

  try {
    const body = await request.json();
    const { params, mode = 'verify' } = body;
    
    if (!params || typeof params !== 'object') {
      return json({
        error: 'Invalid request body. Expected: { "params": { "shop": "...", "signature": "..." }, "mode": "verify" }',
        success: false
      }, { status: 400 });
    }
    
    const validation = validateShopifyProxyRequest(params, sharedSecret, {
      validateTimestamp: true,
      maxAgeSeconds: 300,
      requireCustomerId: false
    });
    
    const debug = mode === 'debug' ? debugSignatureCalculation(params, sharedSecret) : null;
    
    return json({
      success: true,
      mode,
      params,
      validation,
      debug,
      message: validation.isValid ? 'Signature is valid!' : 'Signature is invalid!'
    });
    
  } catch (error) {
    return json({
      error: 'Invalid JSON body',
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false
    }, { status: 400 });
  }
};
