# Shopify App Proxy Setup Guide

## Tổng quan

Shopify App Proxy cho phép customer của shop truy cập vào ứng dụng của bạn thông qua domain của shop (ví dụ: `shop.myshopify.com/apps/dashboard`) thay vì phải redirect sang domain khác.

## Cách hoạt động

1. **Customer truy cập**: `shop.myshopify.com/apps/dashboard`
2. **Shopify tự động**:
   - Thêm các parameters: `shop`, `logged_in_customer_id`, `timestamp`, `signature`, `path_prefix`
   - Forward request đến Remix app của bạn
3. **Remix app**: Verify signature và hiển thị dashboard

## Setup App Proxy trong Shopify Partner Dashboard

### Bước 1: Truy cập Partner Dashboard
1. Đăng nhập vào [Shopify Partner Dashboard](https://partners.shopify.com)
2. Chọn app của bạn
3. Vào **Configuration** > **App proxy**

### Bước 2: <PERSON><PERSON><PERSON> hình App Proxy
```
Subpath prefix: apps
Subpath: dashboard  
Proxy URL: https://your-remix-app-domain.com/dashboard
```

**Kết quả**: `shop.myshopify.com/apps/dashboard` → `your-remix-app.com/dashboard`

### Bước 3: Cấu hình Environment Variables
Trong file `.env`:
```env
SHOPIFY_SHARED_SECRET=your_app_shared_secret_here
SHOPIFY_STOREFRONT_API_TOKEN=your_storefront_token_here
```

## Signature Verification

### Signature là gì?
- **HMAC SHA-256** của các parameters khác
- **Key**: App's shared secret từ Shopify
- **Purpose**: Đảm bảo request đến từ Shopify, không bị giả mạo

### Cách Shopify tạo signature:
```javascript
// Pseudo code
const params = {
  shop: "shop.myshopify.com",
  logged_in_customer_id: "123456",
  path_prefix: "/apps/dashboard", 
  timestamp: "1317327555"
};

// Sort parameters alphabetically and concatenate
const sortedParams = "logged_in_customer_id=123456path_prefix=/apps/dashboardshop=shop.myshopify.comtimestamp=1317327555";

// Calculate HMAC
const signature = crypto.createHmac('sha256', SHARED_SECRET)
  .update(sortedParams)
  .digest('hex');
```

### Verification trong code:
```typescript
import { validateShopifyProxyRequest } from "../utils/shopifySignature";

const validation = validateShopifyProxyRequest(queryParams, sharedSecret, {
  validateTimestamp: true,
  maxAgeSeconds: 300, // 5 minutes
  requireCustomerId: false
});

if (!validation.isValid) {
  throw new Response("Unauthorized", { status: 401 });
}
```

## Security Features

### 1. Signature Verification
- Verify HMAC SHA-256 signature
- Prevent request tampering

### 2. Timestamp Validation  
- Check request freshness (default: 5 minutes)
- Prevent replay attacks

### 3. Session Management
- Store verified data in localStorage
- Auto-expire sessions
- Clean URLs (remove sensitive parameters)

### 4. Customer Authentication
- Verify logged-in customer ID
- Fetch customer data securely

## Flow Diagram

```
Customer → shop.myshopify.com/apps/dashboard
    ↓
Shopify adds: shop, logged_in_customer_id, timestamp, signature
    ↓  
Forward to: your-app.com/dashboard?shop=...&logged_in_customer_id=...&signature=...
    ↓
Remix App:
  1. Verify signature ✓
  2. Validate timestamp ✓  
  3. Store session data ✓
  4. Clean URL ✓
  5. Show dashboard ✓
```

## Troubleshooting

### Common Issues:

1. **Invalid Signature**
   - Check `SHOPIFY_SHARED_SECRET` in `.env`
   - Ensure it matches Partner Dashboard

2. **Timestamp Expired**
   - Check server time synchronization
   - Adjust `maxAgeSeconds` if needed

3. **No Customer ID**
   - Customer chưa đăng nhập vào shop
   - Redirect to shop login page

4. **App Proxy Not Working**
   - Check Partner Dashboard configuration
   - Verify Proxy URL is correct
   - Ensure app is installed on shop

### Debug Tips:
```typescript
console.log("Query params:", queryParams);
console.log("Signature validation:", validation);
console.log("Customer info:", extractCustomerInfo(queryParams));
```

## Best Practices

1. **Always verify signatures** in production
2. **Use HTTPS** for Proxy URL
3. **Validate timestamps** to prevent replay attacks
4. **Store minimal data** in localStorage
5. **Clean URLs** after processing
6. **Handle errors gracefully**
7. **Log security events** for monitoring

## Testing

### Local Development:
1. Use ngrok or similar tunneling service
2. Update Proxy URL in Partner Dashboard
3. Test with real shop and customer

### Production:
1. Deploy to production domain
2. Update Proxy URL in Partner Dashboard  
3. Test end-to-end flow
