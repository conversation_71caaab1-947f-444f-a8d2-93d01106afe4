import WelcomeSection from "./dashboard/WelcomeSection";
import KPICard from "./dashboard/KPICard";
import TopProducts from "./dashboard/TopProducts";
import type { ICustomer } from "app/types/shopify-data";
import { useLocation } from "../contexts/LocationContext";
import { useClientStats } from "../hooks/useCustomerActions";
import { useEffect, useState, useMemo } from "react";

interface Location {
  id: string;
  name: string;
  externalId: string;
}

export default function Dashboard({ customer }: { customer?: ICustomer }) {
  const { selectedLocation, setSelectedLocation, locationId } = useLocation();
  const [selectLocationMode, setSelectLocationMode] = useState(false);
  const [locationSearchTerm, setLocationSearchTerm] = useState("");

  const { data: clientStats, isLoading: statsLoading } = useClientStats(
    customer,
    selectedLocation ? [selectedLocation.externalId] : customer?.companyContactProfiles?.[0]?.roleAssignments?.nodes?.map(loc => loc.companyLocation.externalId || '').filter(Boolean) || []
  );

  const listLocations = customer?.companyContactProfiles?.map(profile => {
    return {
      companyName: profile.company.name,
      companyId: profile.company.id,
      locations: profile.company.locations.nodes.map(loc => ({
        id: loc.id,
        name: loc.name,
        externalId: loc.externalId
      })),
      roles: profile.roleAssignments.nodes.map(role => ({
        id: role.companyLocation.id,
        name: role.companyLocation.name,
        externalId: role.companyLocation.externalId || '',
        roleName: role.role.name
      }))
    };
  });

  const listCompanies = customer?.companyContactProfiles?.map(profile => {
    return {
      companyName: profile.company.name.split('|')[0]?.trim(),
      externalId: profile.company.externalId,
      companyId: profile.company.id,
    };
  });

  const clientCode = listCompanies?.[0]?.externalId;
  const clientName = listCompanies?.[0]?.companyName;

  const availableLocations = useMemo(() => listLocations?.[0]?.roles || [], [listLocations]);

  useEffect(() => {
    if (!selectedLocation && locationId && availableLocations.length > 0) {
      const location = availableLocations.find(loc => loc.id.includes(locationId));
      if (location) {
        setSelectedLocation(location);
      }
    }
  }, [locationId, availableLocations, selectedLocation, setSelectedLocation]);

  const handleLocationSelect = (location: Location) => {
    setSelectedLocation(location);
    setSelectLocationMode(false);
    setLocationSearchTerm("");
  };

  const kpiData = {
    totalSpend: {
      value: clientStats ? `£${clientStats.totalSpent?.toLocaleString() || '0'}` : '£0',
      change: '+0%',
      changeType: 'neutral' as const,
    },
    topProducts: {
      value: clientStats?.topProducts?.length || 0,
      change: '+0%',
      changeType: 'neutral' as const,
    },
    activeSites: {
      value: clientStats?.activeSites || 0,
      change: '+0%',
      changeType: 'neutral' as const,
    },
    ordersThisMonth: {
      value: clientStats?.activeOrders || 0,
      change: '+0%',
      changeType: 'neutral' as const,
    },
  };

  const topProductsData = clientStats?.topProducts?.map((product, index) => ({
    name: product.productName,
    category: 'Product Category',
    count: product.count,
    change: '+0%',
    changeType: 'neutral' as const,
  })) || [];

  if (selectLocationMode && availableLocations.length > 0) {
    const filteredLocations = availableLocations.filter(location =>
      location.name.toLowerCase().includes(locationSearchTerm.toLowerCase()) ||
      location.externalId?.toLowerCase().includes(locationSearchTerm.toLowerCase())
    );

    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Select Location</h2>
          <p className="text-gray-600 mb-6">Please select a location to view the dashboard:</p>
          
          {/* Search Input */}
          <div className="mb-6">
            <div className="relative">
              <input
                type="text"
                placeholder="Search locations by name or ID..."
                value={locationSearchTerm}
                onChange={(e) => setLocationSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pl-10"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
            </div>
          </div>

          {/* Results Count */}
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              Showing {filteredLocations.length} of {availableLocations.length} locations
            </p>
          </div>

          {/* Location List */}
          <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
            {/* All Locations Option */}
            <button
              onClick={() => {
                setSelectedLocation(null);
                setSelectLocationMode(false);
                setLocationSearchTerm("");
              }}
              className={`w-full p-4 border-b border-gray-200 hover:bg-blue-50 transition-colors text-left ${selectedLocation === null ? 'bg-blue-50 border-blue-200' : 'bg-white'}`} 
            >
              <h3 className={`font-semibold ${selectedLocation === null ? 'text-blue-900' : 'text-gray-900'}`}>All Locations</h3>
              <p className={`text-sm ${selectedLocation === null ? 'text-blue-700' : 'text-gray-500'}`}>View data for all assigned locations</p>
            </button>

            {/* Individual Location Options */}
            {filteredLocations.length === 0 && locationSearchTerm ? (
              <div className="p-4 text-center text-gray-500">
                No locations found matching "{locationSearchTerm}"
              </div>
            ) : (
              filteredLocations.map((location) => (
                <button
                  key={location.id}
                  onClick={() => handleLocationSelect(location)}
                  className={`w-full p-4 border-b border-gray-200 hover:bg-blue-50 transition-colors text-left last:border-b-0 ${location.id === selectedLocation?.id ? 'bg-blue-50 border-blue-200' : 'bg-white'}`}
                >
                  <h3 className={`font-semibold ${location.id === selectedLocation?.id ? 'text-blue-900' : 'text-gray-900'}`}>
                    {location.name}
                  </h3>
                  <p className={`text-sm ${location.id === selectedLocation?.id ? 'text-blue-700' : 'text-gray-500'}`}>
                    ID: {location.externalId}
                  </p>
                  <p className={`text-xs ${location.id === selectedLocation?.id ? 'text-blue-600' : 'text-gray-400'}`}>
                    Role: {location.roleName}
                  </p>
                </button>
              ))
            )}
          </div>

          {/* Cancel Button */}
          <div className="mt-6 flex justify-end">
            <button
              onClick={() => {
                setSelectLocationMode(false);
                setLocationSearchTerm("");
              }}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (availableLocations.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">No Locations Available</h2>
          <p className="text-gray-600">No locations are available for this customer.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-500">Selected Location:</span>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${!selectedLocation || selectedLocation === null
            ? "bg-green-100 text-green-800"
            : "bg-blue-100 text-blue-800"
            }`}>
            {selectedLocation?.name || "All Locations"}
          </span>
          <button
            onClick={() => setSelectLocationMode(true)}
            className="text-sm cursor-pointer text-gray-500 hover:text-gray-700 underline"
          >
            Change Location
          </button>
        </div>
      </div>

      <WelcomeSection
        userName={[customer?.firstName, customer?.lastName].filter(Boolean).join(' ')}
        userRole={customer?.email || ''}
        clientCode={clientCode}
        clientName={clientName}
        customer={customer}
      />

      {statsLoading ?
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-32 bg-gray-200 rounded"></div>
          ))}
        </div>
        :
        <div className="flex">
          <div className="flex-1 pr-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <KPICard
                title="Total Spend"
                value={kpiData.totalSpend.value}
                change={kpiData.totalSpend.change}
                changeType={kpiData.totalSpend.changeType}
                icon="fa-pound-sign"
                description="Total spend from API data"
                chartId="total-spend-chart"
                chartType="areaspline"
                chartData={[5000, 7500, 6800, 11200, 13500, 14800, 16500, 18200, 21000, clientStats?.totalSpent || 0]}
                chartColor="#0ea5e9"
              />

              <KPICard
                title="Top Products"
                value={kpiData.topProducts.value}
                change={kpiData.topProducts.change}
                changeType={kpiData.topProducts.changeType}
                icon="fa-box"
                description="Unique products from API data"
                chartId="top-products-chart"
                chartType="column"
                chartData={[32, 35, 38, 42, 45, 41, 44, 46, clientStats?.topProducts?.length || 0]}
                chartColor="#0284c7"
              />

              <KPICard
                title="Active Sites"
                value={kpiData.activeSites.value}
                change={kpiData.activeSites.change}
                changeType={kpiData.activeSites.changeType}
                icon="fa-map-marker-alt"
                description="Active sites from API data"
                chartId="active-sites-chart"
                chartType="line"
                chartData={[10, 10, 11, 12, 12, 12, 12, 12, 12, clientStats?.activeSites || 0]}
                chartColor="#0369a1"
              />

              <KPICard
                title="Orders This Month"
                value={kpiData.ordersThisMonth.value}
                change={kpiData.ordersThisMonth.change}
                changeType={kpiData.ordersThisMonth.changeType}
                icon="fa-shopping-cart"
                description="Orders from API data"
                chartId="orders-chart"
                chartType="column"
                chartData={[42, 40, 38, 45, 43, 39, 41, 38, 39, clientStats?.activeOrders || 0]}
                chartColor="#075985"
              />
            </div>

            <TopProducts products={topProductsData.length > 0 ? topProductsData : [
              {
                name: "No products data available",
                category: "N/A",
                count: 0,
                change: "+0%",
                changeType: "neutral" as const,
              }
            ]} />
            {/* 
            <UsageBreakdown />

            <RecentOrders orders={staticData.recentOrders} /> */}
          </div>
        </div>
      }
    </div>
  );
}