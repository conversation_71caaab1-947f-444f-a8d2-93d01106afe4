import { SEARCH_CUSTOMERS_QUERY, GET_CUSTOMER_BY_ID, SEARCH_CUSTOMER_LOCATIONS_QUERY } from "../graphql";

export interface CustomerSearchParams {
  query?: string;
  first?: number;
  after?: string;
}

export interface CustomerLocationSearchParams {
  customerId: string;
  searchTerm?: string;
  first?: number;
}

export class CustomerService {
  constructor(private admin: any) { }

  async searchCustomers(params: CustomerSearchParams) {
    try {
      const response = await this.admin.graphql(SEARCH_CUSTOMERS_QUERY, {
        variables: {
          query: params.query || "",
          first: params.first || 50,
          after: params.after ? params.after : null,
        },
      });

      const responseJson = await response.json() as any;

      if (responseJson.errors) {
        throw new Error(responseJson.errors[0].message);
      }

      return {
        customers: responseJson.data.customers.nodes,
        pageInfo: responseJson.data.customers.pageInfo,
        success: true,
      };
    } catch (error) {
      console.error("Error searching customers:", error);
      return {
        customers: [],
        error: error instanceof Error ? error.message : "Failed to search customers",
      };
    }
  }

  async getCustomerById(id: string) {
    try {
      const response = await this.admin.graphql(GET_CUSTOMER_BY_ID, {
        variables: {
          id,
        },
      });

      const responseJson = await response.json() as any;

      if (responseJson.errors) {
        throw new Error(responseJson.errors[0].message);
      }

      return {
        customer: responseJson.data.customer,
        success: true,
      };
    } catch (error) {
      console.error("Error searching customers:", error);
      return {
        customer: null,
        error: error instanceof Error ? error.message : "Failed to search customers",
      };
    }
  }

  async searchCustomerLocations(params: CustomerLocationSearchParams) {
    try {
      const response = await this.admin.graphql(SEARCH_CUSTOMER_LOCATIONS_QUERY, {
        variables: {
          customerId: params.customerId,
          searchTerm: params.searchTerm || "",
          first: params.first || 50,
        },
      });

      const responseJson = await response.json() as any;

      if (responseJson.errors) {
        throw new Error(responseJson.errors[0].message);
      }

      const locations = responseJson.data.customer?.companyContactProfiles?.[0]?.roleAssignments?.nodes || [];
      const pageInfo = responseJson.data.customer?.companyContactProfiles?.[0]?.roleAssignments?.pageInfo;

      return {
        locations,
        pageInfo,
        success: true,
      };
    } catch (error) {
      console.error("Error searching customer locations:", error);
      return {
        locations: [],
        error: error instanceof Error ? error.message : "Failed to search customer locations",
      };
    }
  }
}
