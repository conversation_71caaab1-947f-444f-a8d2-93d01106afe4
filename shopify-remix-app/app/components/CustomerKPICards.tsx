import React from 'react';
import { Customer } from './AdminDashboard';
import { ClientStatsData } from 'app/types/dashboard';

interface CustomerKPICardsProps {
  customer: Customer;
  clientStats: ClientStatsData | null;
  isLoading: boolean;
}

export const CustomerKPICardsSkeleton: React.FC = () => {
  return (
    <div className="rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="grid grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((index) => (
          <div key={index} className="text-center bg-white rounded-lg p-4 py-8">
            <div className="flex items-center justify-center mb-2">
              <div className="w-4 h-4 bg-gray-300 rounded mr-2 animate-pulse"></div>
              <div className="w-20 h-3 bg-gray-300 rounded animate-pulse"></div>
            </div>
            <div className="w-24 h-8 bg-gray-300 rounded mx-auto animate-pulse"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const CustomerKPICards: React.FC<CustomerKPICardsProps> = ({ customer, clientStats, isLoading  }) => {
  if (isLoading) {
    return <CustomerKPICardsSkeleton />;
  }
  return (
    <div className="rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="grid grid-cols-4 gap-6">
        <div className="text-center bg-white rounded-lg p-4 py-8">
          <div className="flex items-center justify-center mb-2">
            <i className="fas fa-pound-sign text-blue-500 mr-2"></i>
            <span className="text-sm text-gray-600">Total Spend</span>
          </div>
          <div className="text-2xl font-bold text-gray-800">
            £{Number(clientStats?.totalSpent || 0).toLocaleString('en-GB', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}{' '}
            {/* <span className="text-green-500 text-sm">+</span> */}
          </div>
        </div>

        <div className="text-center bg-white rounded-lg p-4 py-8">
          <div className="flex items-center justify-center mb-2">
            <i className="fas fa-map-marker-alt text-blue-500 mr-2"></i>
            <span className="text-sm text-gray-600">Active Sites</span>
          </div>
          <div className="text-2xl font-bold text-gray-800">{clientStats?.activeSites || 0}</div>
        </div>

        <div className="text-center bg-white rounded-lg p-4 py-8">
          <div className="flex items-center justify-center mb-2">
            <i className="fas fa-shopping-cart text-blue-500 mr-2"></i>
            <span className="text-sm text-gray-600">Orders YTD</span>
          </div>
          <div className="text-2xl font-bold text-gray-800">{clientStats?.activeOrders || 0}</div>
        </div>

        <div className="text-center bg-white rounded-lg p-4 py-8">
          <div className="flex items-center justify-center mb-2">
            <i className="fas fa-heart text-green-500 mr-2"></i>
            <span className="text-sm text-gray-600">Account Health</span>
          </div>
          {customer.status === "ENABLED" ? (
            <div className="text-xl font-bold text-green-600">Excellent</div>
          ) : (
            <div className="text-xl font-bold text-grey-600">Not Enabled</div>
          )}
        </div>
      </div>
    </div>
  );
};
