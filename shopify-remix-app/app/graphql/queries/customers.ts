export const SEARCH_CUSTOMERS_QUERY = `#graphql
  query searchCustomers($query: String, $first: Int!, $after: String) {
    customers(first: $first, query: $query, after: $after) {
      nodes {
        id
        firstName
        lastName
        displayName
        email
        phone
        amountSpent {
          amount
          currencyCode
        }
        createdAt
        lifetimeDuration
        numberOfOrders
        state
        events(first: 50) {
          nodes {
            action
            appTitle
            attributeToApp
            attributeToUser
            createdAt
            criticalAlert
            id
            message
          }
        }
        companyContactProfiles {
          company {
            id
            name
            externalId
            locations(first: 50) {
              nodes {
                id
                name
                externalId
              }
            }
          }
          roleAssignments(first: 50) {
            nodes {
              id
              company {
                id
                name
                externalId
              }
              companyLocation {
                id
                name
                externalId
              }
              role {
                name
              }
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

export const GET_CUSTOMER_BY_ID = `#graphql
  query getCustomerById($id: ID!) {
    customer(id: $id) {
      id
      firstName
      lastName
      displayName
      email
      phone
      amountSpent {
        amount
        currencyCode
      }
      createdAt
      lifetimeDuration
      numberOfOrders
      state
      events(first: 50) {
        nodes {
          action
          appTitle
          attributeToApp
          attributeToUser
          createdAt
          criticalAlert
          id
          message
        }
      }
      companyContactProfiles {
        company {
          id
          name
          externalId
          locations(first: 50) {
            nodes {
              id
              name
              externalId
            }
          }
        }
        roleAssignments(first: 50) {
          nodes {
            id
            company {
              id
              name
              externalId
            }
            companyLocation {
              id
              name
              externalId
            }
            role {
              name
            }
          }
          pageInfo {
            hasNextPage
            hasPreviousPage
            startCursor
            endCursor
          }
        }
      }
    }
  }
`;

export const SEARCH_CUSTOMER_LOCATIONS_QUERY = `#graphql
  query searchCustomerLocations($customerId: ID!, $searchTerm: String, $first: Int = 50) {
    customer(id: $customerId) {
      id
      companyContactProfiles {
        roleAssignments(first: $first, query: $searchTerm) {
          nodes {
            id
            company {
              id
              name
              externalId
            }
            companyLocation {
              id
              name
              externalId
            }
            role {
              name
            }
          }
          pageInfo {
            hasNextPage
            hasPreviousPage
            startCursor
            endCursor
          }
        }
      }
    }
  }
`;
