

const BC_API_CONFIG = {
  baseUrl: 'https://api.businesscentral.dynamics.com/v2.0',
  tenantId: '489d30d4-7f89-402e-9357-aaa87d5312c8',
  clientId: '1ef84ab8-1ae4-48b2-8469-03541c46f3ea',
  clientSecret: '****************************************',
  grantType: 'client_credentials',
  environment: 'Production',
  company: 'MSUPPLY LIVE',
  companyId: 'f50b5790-3115-f011-9346-6045bdd2a729',
  scope: 'https://api.businesscentral.dynamics.com/.default'
};

const BC_DEV_API_CONFIG = {
  ...BC_API_CONFIG,
  environment: 'development-v26',
};

type TBCCustomer = {
  '@odata.etag': string;
  number: string;
  displayName: string;
  email: string;
}

class BCHelper {
  private token: string | null = null;
  private tokenExpiry: number | null = null;

  constructor() {
    this.getCustomers();
  }

  customers : TBCCustomer[] = [];

  /**
   * Get or refresh the Business Central access token
   */
  async getToken(): Promise<string> {
    // Check if we have a valid token
    if (this.token && this.tokenExpiry && Date.now() < this.tokenExpiry) {
      return this.token;
    }

    const tokenEndpoint = `https://login.microsoftonline.com/${BC_API_CONFIG.tenantId}/oauth2/v2.0/token`;

    const params = new URLSearchParams({
      client_id: BC_API_CONFIG.clientId,
      client_secret: BC_API_CONFIG.clientSecret,
      scope: BC_API_CONFIG.scope,
      grant_type: BC_API_CONFIG.grantType
    });

    try {
      console.log('Requesting BC token...');
      const response = await fetch(tokenEndpoint, {
        method: 'POST',
        body: params,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get token: ${response.status} ${response.statusText}\n${errorText}`);
      }

      const data = await response.json();
      console.log('Successfully obtained BC token');
      
      // Store token and set expiry (subtract 5 minutes for safety)
      this.token = data.access_token;
      this.tokenExpiry = Date.now() + (data.expires_in - 300) * 1000;
      
      return this.token;
    } catch (error) {
      console.error('Error getting BC token:', error);
      throw error;
    }
  }

  /**
   * Build the full URL for Business Central API calls
   */
  private buildODataV4Url(path: string): string {
    // Remove leading slash if present
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return `${BC_API_CONFIG.baseUrl}/${BC_API_CONFIG.tenantId}/${BC_API_CONFIG.environment}/ODataV4/Company('${BC_API_CONFIG.company}')/${cleanPath}`;
  }

  async getPaginatedData(url: string, options: RequestInit = {}): Promise<any[]> {
    try {
      console.log(`Fetching data from URL: ${url}`);
      const token = await this.getToken();
      let allRecords: any[] = [];
      let nextLink = url;

      while (nextLink) {
        const response = await fetch(nextLink, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            ...options.headers
          },
          ...options
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Failed to fetch data from ${url}: ${response.status} ${response.statusText}\n${errorText}`);
        }

        const data = await response.json();
        if (data.value) {
          allRecords = [...allRecords, ...data.value];
        }

        nextLink = data['@odata.nextLink'] || null;
        console.log(`📊 Fetched ${data.value?.length || 0} records from ${url}. Total records so far: ${allRecords.length}`);
      }

      console.log(`✅ Successfully fetched all data from ${url}`);
      console.log(`📊 Total records: ${allRecords.length}`);

      return allRecords;
    } catch (error) {
      console.error(`❌ Error fetching data from ${url}:`, error);
      throw error;
    }
  }
  

  /**
   * Fetch data from Business Central API with pagination support
   * @param path - The API endpoint path (e.g., '/SalesPriceLines')
   * @param options - Optional fetch options
   * @returns Promise with all paginated data
   */
  async get(path: string, options: RequestInit = {}): Promise<any[]> {
    console.log(`Fetching data from BC API: ${path}`);
    return this.getPaginatedData(this.buildODataV4Url(path), options);
  }

  /**
   * Fetch data from Business Central API without pagination (first page only)
   * @param path - The API endpoint path (e.g., '/SalesPriceLines')
   * @param options - Optional fetch options
   * @returns Promise with first page data
   */
  async getOne(path: string, options: RequestInit = {}): Promise<any[]> {
    try {
      console.log(`Fetching first page from BC API: ${path}`);
      const token = await this.getToken();
      const url = this.buildODataV4Url(path);
      console.log(url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch data from ${path}: ${response.status} ${response.statusText}\n${errorText}`);
      }

      const data = await response.json();
      const records = data.value || [];
      
      console.log(`✅ Successfully fetched first page from ${path}`);
      console.log(`📊 Records in first page: ${records.length}`);

      return records;
    } catch (error) {
      console.error(`❌ Error fetching data from ${path}:`, error);
      throw error;
    }
  }

  /**
   * Build the full URL for Business Central API calls
   */
  private buildALUrl(path: string, query: string = ''): string {
    // Remove leading slash if present
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return `${BC_API_CONFIG.baseUrl}/${BC_API_CONFIG.tenantId}/${BC_API_CONFIG.environment}/api/personify/sales/v1.0/${cleanPath}?company=${BC_API_CONFIG.company}${!query ? '' : `&${query}`}`;
  }

  /**
   * Fetch data from Business Central API with pagination support, AL API
   * @param path - The API endpoint path (e.g., '/SalesPriceLines')
   * @param options - Optional fetch options
   * @returns Promise with all paginated data
   */
  async getAL(path: string, query: string = ''): Promise<any[]> {
    console.log(`Fetching data from BC AL API: ${path}`);
    return this.getPaginatedData(this.buildALUrl(path, query));
  }

  async getCustomers() {
    if (this.customers.length > 0) {
      return this.customers;
    }
    const url = `${BC_API_CONFIG.baseUrl}/${BC_API_CONFIG.tenantId}/${BC_API_CONFIG.environment}/api/v2.0/customers?company=${BC_API_CONFIG.company}`;
    const customers = await this.getPaginatedData(url);
    this.customers = customers;
    console.log(`Fetched ${customers.length} customers`);
    return customers;
  }
}

export default new BCHelper();