import type { LoaderFunctionArgs } from "@remix-run/node";
import { GET_CUSTOMER_BY_ID } from "app/graphql";

// Helper function to fetch all role assignments with pagination
async function fetchAllRoleAssignments(shop: string, storefrontAccessToken: string, customerId: string) {
  const allRoleAssignments: any[] = [];
  let hasNextPage = true;
  let cursor: string | null = null;

  while (hasNextPage) {
    const query = `#graphql
      query getCustomerRoleAssignments($id: ID!, $after: String) {
        customer(id: $id) {
          companyContactProfiles {
            roleAssignments(first: 250, after: $after) {
              nodes {
                id
                company {
                  id
                  name
                  externalId
                }
                companyLocation {
                  id
                  name
                  externalId
                }
                role {
                  name
                }
              }
              pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
              }
            }
          }
        }
      }
    `;

    const resp: Response = await fetch(`https://${shop}/admin/api/2025-07/graphql.json`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": storefrontAccessToken,
      },
      body: JSON.stringify({
        query,
        variables: {
          id: customerId,
          after: cursor,
        },
      }),
    });

    const data: any = await resp.json();
    
    if (data.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(data.errors)}`);
    }

    const roleAssignments: any = data.data?.customer?.companyContactProfiles?.[0]?.roleAssignments;
    if (roleAssignments) {
      allRoleAssignments.push(...roleAssignments.nodes);
      hasNextPage = roleAssignments.pageInfo.hasNextPage;
      cursor = roleAssignments.pageInfo.endCursor;
    } else {
      hasNextPage = false;
    }
  }

  return allRoleAssignments;
}

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const { id } = params;
  
  if (!id) {
    return new Response(
      JSON.stringify({ error: "Customer ID is required" }), 
      { 
        status: 400,
        headers: { "Content-Type": "application/json" }
      }
    );
  }

  try {
    const gid = `gid://shopify/Customer/${id}`;
    const shop = 'maintenance-supply.myshopify.com';
    const storefrontAccessToken = process.env.SHOPIFY_STOREFRONT_API_TOKEN || '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';

    const resp = await fetch(`https://${shop}/admin/api/2025-07/graphql.json`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": storefrontAccessToken,
      },
      body: JSON.stringify({
        query: GET_CUSTOMER_BY_ID,
        variables: {
          id: gid,
        },
      }),
    });

    const data = await resp.json();
    console.log("Customer API response:", JSON.stringify(data));

    if (data.data?.customer) {
      const customerData = data.data.customer;
      
      if (customerData.companyContactProfiles?.[0]?.roleAssignments?.pageInfo?.hasNextPage) {
        const allRoleAssignments = await fetchAllRoleAssignments(shop, storefrontAccessToken, gid);
        
        if (customerData.companyContactProfiles?.[0]) {
          customerData.companyContactProfiles[0].roleAssignments = {
            nodes: allRoleAssignments,
            pageInfo: {
              hasNextPage: false,
              hasPreviousPage: false,
              startCursor: null,
              endCursor: null
            }
          };
        }
      }

      return new Response(
        JSON.stringify({ 
          success: true, 
          customer: customerData 
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
    } else if (data.errors) {
      console.error('GraphQL errors:', data.errors);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Failed to fetch customer data",
          details: data.errors 
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    } else {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Customer not found" 
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  } catch (error) {
    console.error('Error fetching customer data:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: "Internal server error" 
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};
