import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAd<PERSON>, check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";
import * as XLSX from 'xlsx';
import { transformSalesInvoiceHeaders, transformSalesInvoiceLines } from './utils/fieldMapping';
import * as moment from 'moment';
import * as fs from 'fs';

const DEBUG = false

class GenSiteReport implements TypeAPIHandler {

    url = "/api/bc/stats";
    method = "POST";
    apiSchema = {
        body: Joi.object({
            client: Joi.string(),
            postingDateFrom: Joi.string().required(),
            postingDateTo: Joi.string().required(),
            filterLocations: Joi.array().items(Joi.string()).optional(),
        }),
    }

    preHandler = combineMiddlewares([
        validateRequest(this.apiSchema),
    ]);

    handler = async (request: TRequestUser, reply) => {
        const headers = request.headers;
        if (headers['x-api-key'] !== 'kgmdqtveva') {
            return reply.status(401).send({
                success: false,
                message: 'Unauthorized',
            });
        }

        const { client, postingDateFrom, postingDateTo, filterLocations } = request.body;
        
        let saleHeaders = await DB.BCSaleInvoiceHeader.findAll({
            where: {
                BilltoCustomerNo: client,
                PostingDate: {
                    [Op.between]: [postingDateFrom, postingDateTo],
                },
                ...(filterLocations && filterLocations.length > 0 ? {
                    SelltoCustomerNo: {
                        [Op.in]: filterLocations,
                    },
                } : {}),
            },
            raw: true,
            logging: console.log,
        });
        DEBUG && fs.writeFileSync(__dirname + '/saleHeaders.json', JSON.stringify(saleHeaders, null, 2));

        const saleLines = await DB.BCSaleInvoiceLine.findAll({
            where: {
                DocumentNo: {
                    [Op.in]: saleHeaders.map(header => header.No),
                },
            },
            raw: true,
        });
        DEBUG && fs.writeFileSync(__dirname + '/saleLines.json', JSON.stringify(saleLines, null, 2));

        const saleInvoiceHeader = transformSalesInvoiceHeaders(saleHeaders);
        DEBUG && fs.writeFileSync(__dirname + '/saleInvoiceHeader.json', JSON.stringify(saleInvoiceHeader, null, 2));
        const saleInvoiceLine = transformSalesInvoiceLines(saleLines);
        DEBUG && fs.writeFileSync(__dirname + '/saleInvoiceLine.json', JSON.stringify(saleInvoiceLine, null, 2));
        const contractLines = require('./utils/contract_line.json');

        let totalSpent = 0;


        let sites = {};
        let orders = {};
        let products = {};

        await BCHelper.getCustomers();

        saleInvoiceHeader.forEach(header => {
            const items = saleInvoiceLine.filter(line => line['Document No.'] === header['No.']);
            let site = BCHelper.customers.find(c => c.number === header['Sell-to Customer No.'])?.displayName;
            if (!site) {
                site = header['Ship-to Name'];
            }
            sites[site] = 1;
            orders[header['No.']] = 1;
            items.forEach(item => {
                const findContractLine = contractLines.find(cl => cl['No.'] === item['No.'] && cl['Bill-to Customer'] === header['Bill-to Customer No.']);
                const quantity = +item['Quantity'];
                const vatPercent = +item['VAT %'] / 100;
                if (!quantity) return;
                if (findContractLine) {
                    totalSpent += +item['Line Amount'] * quantity * (1 + vatPercent);
                    const productName = item['Description'];
                    products[productName] = (products[productName] || 0) + 1;
                }
            });
        });

        const productArrays = Object.entries(products).map(([productName, count]) => ({
            productName,
            count,
        }));
        const topProducts = productArrays.sort((a : any, b : any) => b.count - a.count).slice(0, 10);


        return {
            success: true,
            data: {
                totalSpent,
                activeSites: Object.keys(sites).length,
                activeOrders: Object.keys(orders).length,
                topProducts,
            },
        }
    };
}

export default new GenSiteReport();
