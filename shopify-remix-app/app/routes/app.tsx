import type { ActionFunctionArgs, HeadersFunction, LoaderFunctionArgs } from "@remix-run/node";
import { Outlet, useRouteError, useLoaderData } from "@remix-run/react";
import { boundary } from "@shopify/shopify-app-remix/server";
import { AppProvider } from "@shopify/shopify-app-remix/react";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";

import { authenticate } from "../shopify.server";
import AdminDashboard from "../components/AdminDashboard";
import Dashboard from "../components/Dashboard";
import { useEffect, useState } from "react";
import { useCustomerFetcher } from "app/hooks/useCustomerActions";
import { LocationProvider } from "../contexts/LocationContext";

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return { 
    apiKey: process.env.SHOPIFY_API_KEY || "",
    appUrl: process.env.SHOPIFY_APP_URL || ""
  };
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);

  const formData = await request.formData();
  const action = formData.get("action") as string;

  const customerService = new (await import("../services/customerService")).CustomerService(admin);

  switch (action) {
    case "searchCustomers":
      const searchTerm = formData.get("searchTerm") as string;
      const limit = parseInt(formData.get("limit") as string) || 50;
      const cursor = formData.get("cursor") as string;
      return await customerService.searchCustomers({
        query: searchTerm,
        first: limit,
        after: cursor
      });

    case "searchCustomerLocations":
      const customerId = formData.get("customerId") as string;
      const locationSearchTerm = formData.get("searchTerm") as string;
      const locationLimit = parseInt(formData.get("limit") as string) || 100;
      return await customerService.searchCustomerLocations({
        customerId,
        searchTerm: locationSearchTerm,
        first: locationLimit
      });

    default:
      return { error: "Invalid action" };
  }
};

export default function App() {
  const { apiKey } = useLoaderData<typeof loader>();
  const { fetchCustomers, loading, data } = useCustomerFetcher();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'admin' | 'customer'>('admin');

  useEffect(() => {
    setTimeout(() => {
      fetchCustomers("", 50);
    }, 400);
  }, []);

  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      fetchCustomers(searchTerm, 50);
    }, 500);

    return () => clearTimeout(debounceTimeout);
  }, [searchTerm])

  console.log("data", data)

  const onCustomerInspect = (customerId: string) => {
    const customer = data?.customers?.find(c => c.id === customerId);
    if (!customer) return;

    setSelectedCustomer(customer);
    setViewMode('customer');
  }

  const onBackToAdmin = () => {
    setViewMode('admin');
    setSelectedCustomer(null);
  }

  return (
    <AppProvider isEmbeddedApp apiKey={apiKey}>
      {viewMode === 'admin' ? (
        <AdminDashboard
          customers={data?.customers || []}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          isLoadingCustomers={loading}
          onCustomerInspect={onCustomerInspect}
        />
      ) : (
        <LocationProvider>
          <div className="relative">
            <button 
              onClick={onBackToAdmin}
              className="mb-4 z-10 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
              ← Back to Admin Dashboard
            </button>
            <Dashboard customer={selectedCustomer} />
          </div>
        </LocationProvider>
      )}
      <Outlet />
    </AppProvider>
  );
}

export function ErrorBoundary() {
  return boundary.error(useRouteError());
}

export const headers: HeadersFunction = (headersArgs) => {
  return boundary.headers(headersArgs);
};
