import crypto from 'crypto';

/**
 * Shopify App Proxy Signature Verification Utility
 * 
 * This utility verifies HMAC signatures from Shopify App Proxy requests
 * to ensure the request authenticity and prevent tampering.
 */

interface ShopifyProxyParams {
  shop?: string;
  logged_in_customer_id?: string;
  path_prefix?: string;
  timestamp?: string;
  signature?: string;
  [key: string]: string | undefined;
}

/**
 * Verifies Shopify App Proxy signature
 * @param params - Query parameters from the request
 * @param sharedSecret - Your app's shared secret from Shopify
 * @returns boolean - true if signature is valid
 */
export function verifyShopifySignature(
  params: ShopifyProxyParams, 
  sharedSecret: string
): boolean {
  try {
    // Extract signature from params
    const { signature, ...otherParams } = params;
    
    if (!signature) {
      console.warn('No signature provided in request');
      return false;
    }

    // Remove undefined values and convert to string
    const cleanParams: Record<string, string> = {};
    Object.entries(otherParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanParams[key] = String(value);
      }
    });

    // Sort parameters alphabetically by key and create query string
    const sortedParams = Object.keys(cleanParams)
      .sort()
      .map(key => `${key}=${cleanParams[key]}`)
      .join('');

    // Calculate HMAC
    const calculatedSignature = crypto
      .createHmac('sha256', sharedSecret)
      .update(sortedParams)
      .digest('hex');

    // Compare signatures using timing-safe comparison
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(calculatedSignature, 'hex')
    );
  } catch (error) {
    console.error('Error verifying Shopify signature:', error);
    return false;
  }
}

/**
 * Validates timestamp to prevent replay attacks
 * @param timestamp - Timestamp from Shopify request
 * @param maxAgeSeconds - Maximum age in seconds (default: 300 = 5 minutes)
 * @returns boolean - true if timestamp is valid
 */
export function validateTimestamp(
  timestamp?: string, 
  maxAgeSeconds: number = 300
): boolean {
  if (!timestamp) {
    return false;
  }

  try {
    const requestTime = parseInt(timestamp, 10) * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeDifference = Math.abs(currentTime - requestTime);
    
    return timeDifference <= maxAgeSeconds * 1000;
  } catch (error) {
    console.error('Error validating timestamp:', error);
    return false;
  }
}

/**
 * Comprehensive validation for Shopify App Proxy requests
 * @param params - Query parameters from the request
 * @param sharedSecret - Your app's shared secret
 * @param options - Validation options
 * @returns object with validation results
 */
export function validateShopifyProxyRequest(
  params: ShopifyProxyParams,
  sharedSecret: string,
  options: {
    validateTimestamp?: boolean;
    maxAgeSeconds?: number;
    requireCustomerId?: boolean;
  } = {}
) {
  const {
    validateTimestamp: shouldValidateTimestamp = true,
    maxAgeSeconds = 300,
    requireCustomerId = false
  } = options;

  const result = {
    isValid: false,
    errors: [] as string[],
    signatureValid: false,
    timestampValid: false,
    customerIdPresent: false
  };

  // Verify signature
  result.signatureValid = verifyShopifySignature(params, sharedSecret);
  if (!result.signatureValid) {
    result.errors.push('Invalid signature');
  }

  // Validate timestamp if required
  if (shouldValidateTimestamp) {
    result.timestampValid = validateTimestamp(params.timestamp, maxAgeSeconds);
    if (!result.timestampValid) {
      result.errors.push('Invalid or expired timestamp');
    }
  } else {
    result.timestampValid = true;
  }

  // Check customer ID if required
  result.customerIdPresent = !!params.logged_in_customer_id;
  if (requireCustomerId && !result.customerIdPresent) {
    result.errors.push('Customer ID required but not present');
  }

  // Overall validation
  result.isValid = result.signatureValid && 
                   result.timestampValid && 
                   (!requireCustomerId || result.customerIdPresent);

  return result;
}

/**
 * Extract customer information from validated Shopify proxy params
 * @param params - Validated query parameters
 * @returns customer info object
 */
export function extractCustomerInfo(params: ShopifyProxyParams) {
  return {
    customerId: params.logged_in_customer_id || null,
    shop: params.shop || null,
    pathPrefix: params.path_prefix || null,
    timestamp: params.timestamp || null,
    isLoggedIn: !!params.logged_in_customer_id
  };
}
