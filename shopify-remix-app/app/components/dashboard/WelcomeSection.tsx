import { useEffect, useState } from "react";
import { useReportGenerator, type ReportType, type TimeRange } from "app/hooks/useCustomerActions";
import { useLocationForReports } from "app/hooks/useLocationForReports";
import type { ICustomer } from "app/types/shopify-data";

interface WelcomeSectionProps {
  userName: string;
  userRole: string;
  clientCode?: string;
  clientName?: string;
  customer?: ICustomer;
}

export default function WelcomeSection({
  userName,
  userRole,
  clientCode,
  clientName,
  customer,
}: WelcomeSectionProps) {
  const [showReportDropdown, setShowReportDropdown] = useState(false);
  const [showTimeDropdown, setShowTimeDropdown] = useState(false);
  const [selectedReportType, setSelectedReportType] = useState<ReportType | null>(null);

  const { generateReport, isGenerating } = useReportGenerator();
  const { selectedLocation, filterLocations, hasLocation } = useLocationForReports();
  
  const getAllLocationsFilter = () => {
    return customer?.companyContactProfiles?.[0]?.roleAssignments?.nodes?.map(loc => loc.companyLocation.externalId || '').filter(Boolean) || [];
  };
  
  const canGenerateReports = hasLocation || (customer && getAllLocationsFilter().length > 0);
  
  const getReportFilterLocations = () => {
    if (selectedLocation) {
      return filterLocations;
    }
    return getAllLocationsFilter();
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.report-dropdown-container')) {
        setShowReportDropdown(false);
        setShowTimeDropdown(false);
        setSelectedReportType(null);
      }
    };

    if (showReportDropdown || showTimeDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showReportDropdown, showTimeDropdown]);

  const handleReportTypeSelect = (reportType: ReportType) => {
    if (!canGenerateReports) {
      alert('Please select a location first');
      return;
    }
    
    setSelectedReportType(reportType);
    setShowReportDropdown(false);
    setShowTimeDropdown(true);
  };

  const handleTimeRangeSelect = async (timeRange: TimeRange) => {
    if (!selectedReportType || !canGenerateReports) return;

    setShowTimeDropdown(false);

    const params = {
      reportType: selectedReportType,
      timeRange,
      clientCode,
      clientName,
      filterLocations: getReportFilterLocations(),
    };

    const result = await generateReport(params);

    if (!result.success) {
      alert(`Error generating report: ${result.error}`);
    }

    setSelectedReportType(null);
  };

  const handleDownloadReport = () => {
    if (!canGenerateReports) {
      alert('Please select a location first');
      return;
    }
    
    setShowReportDropdown(!showReportDropdown);
    setShowTimeDropdown(false);
    setSelectedReportType(null);
  };

  const handleRefreshData = () => {
    console.log("Refresh Data clicked");
    alert("Refresh Data clicked!");

    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  return (
    <div className="mb-8 flex justify-between">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">
          Welcome back, {userName}
        </h1>
        <p className="text-gray-600">
          {clientName} | {clientCode}
        </p>
      </div>
      <div className="flex items-center">
        <div className="relative report-dropdown-container">
          <button
            onClick={handleDownloadReport}
            disabled={isGenerating || !canGenerateReports}
            className="flex items-center space-x-2 bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ pointerEvents: 'auto' }}
          >
            <span>{isGenerating ? 'Generating...' : 'Download Report'}</span>
            <i className="fa-solid fa-chevron-down"></i>
          </button>

          {/* {!hasLocation && (
            <div className="absolute top-full left-0 mt-1 w-64 bg-yellow-50 border border-yellow-200 rounded-md shadow-lg z-10 p-3">
              <p className="text-yellow-800 text-sm">
                Please select a location to generate reports
              </p>
            </div>
          )} */}

          {showReportDropdown && canGenerateReports && (
            <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-10">
              <button
                onClick={() => handleReportTypeSelect('site')}
                className="w-full cursor-pointer text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 border-b border-gray-200"
              >
                Site Report
              </button>
              <button
                onClick={() => handleReportTypeSelect('sir')}
                className="w-full cursor-pointer text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                Sir Report
              </button>
            </div>
          )}

          {showTimeDropdown && selectedReportType && canGenerateReports && (
            <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-10">
              <button
                onClick={() => handleTimeRangeSelect('last_week')}
                className="w-full cursor-pointer text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 border-b border-gray-200"
              >
                Last Week
              </button>
              <button
                onClick={() => handleTimeRangeSelect('last_month')}
                className="w-full cursor-pointer text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                Last Month
              </button>
            </div>
          )}
        </div>
        <button
          onClick={handleRefreshData}
          onMouseDown={() => console.log("Mouse down on refresh button")}
          onMouseUp={() => console.log("Mouse up on refresh button")}
          className="ml-4 bg-msc-teal hover:bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium cursor-pointer"
          style={{ pointerEvents: 'auto' }}
        >
          Refresh Data
        </button>
      </div>
    </div>
  );
}
