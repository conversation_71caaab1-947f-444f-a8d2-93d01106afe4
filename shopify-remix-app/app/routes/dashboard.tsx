import type { LoaderFunctionArgs } from "@remix-run/node";
import SecureDashboard from "../components/SecureDashboard";
import { useLoaderData, redirect } from "@remix-run/react";
import { GET_CUSTOMER_BY_ID } from "app/graphql";
import type { ICustomer } from "app/types/shopify-data";
import { LocationProvider } from "../contexts/LocationContext";
import { validateShopifyProxyRequest, extractCustomerInfo } from "../utils/shopifySignature";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);

  // Extract all query parameters
  const queryParams = Object.fromEntries(url.searchParams.entries());
  const {
    shop = 'maintenance-supply.myshopify.com', // fallback for development
    logged_in_customer_id: loggedInCustomerId,
    path_prefix: pathPrefix,
    timestamp,
    signature,
    redirected,
    location_id: locationId
  } = queryParams;

  const expectedHost = process.env.SHOPIFY_APP_URL ? new URL(process.env.SHOPIFY_APP_URL).hostname : null;

  console.log("Request headers:", request.headers.get('x-forwarded-host'), expectedHost);

  // Verify Shopify signature if present
  const sharedSecret = process.env.SHOPIFY_SHARED_SECRET;
  if (signature && sharedSecret) {
    const validation = validateShopifyProxyRequest(queryParams, sharedSecret, {
      validateTimestamp: true,
      maxAgeSeconds: 300, // 5 minutes
      requireCustomerId: false // Allow requests without customer ID
    });

    console.log("Signature validation:", validation);

    if (!validation.isValid) {
      console.error("Invalid Shopify signature:", validation.errors);
      // For security, you might want to reject invalid signatures
      // throw new Response("Unauthorized", { status: 401 });
    }
  } else if (signature) {
    console.warn("Signature present but no shared secret configured");
  }

  if ((request.headers.get('x-forwarded-host') === 'shopify.msupply.co.uk'
    || request.headers.get('edge-zone-name') === 'myshopify.com'
  )
    && expectedHost && shop && loggedInCustomerId && !redirected
  ) {
    const redirectUrl = new URL('/dashboard', process.env.SHOPIFY_APP_URL);
    redirectUrl.searchParams.set('shop', shop);
    redirectUrl.searchParams.set('logged_in_customer_id', loggedInCustomerId);
    redirectUrl.searchParams.set('redirected', 'true');

    // Preserve other parameters if they exist
    if (pathPrefix) redirectUrl.searchParams.set('path_prefix', pathPrefix);
    if (timestamp) redirectUrl.searchParams.set('timestamp', timestamp);
    if (signature) redirectUrl.searchParams.set('signature', signature);

    return redirect(redirectUrl.toString());
  }

  try {
    const gid = `gid://shopify/Customer/${loggedInCustomerId}`;

    const storefrontAccessToken = process.env.SHOPIFY_STOREFRONT_API_TOKEN || '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';

    const resp = await fetch(`https://${shop}/admin/api/2025-07/graphql.json`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": storefrontAccessToken,
      },
      body: JSON.stringify({
        query: GET_CUSTOMER_BY_ID,
        variables: {
          id: gid,
        },
      }),
    });

    const data = await resp.json();
    console.log("Storefront API response:", JSON.stringify(data));

    if (data.data?.customer) {
      const customer = data.data.customer as ICustomer;
      return {
        shop,
        loggedInCustomerId,
        pathPrefix,
        timestamp,
        signature,
        customer,
        locationId,
        shopifyLoginUrl: `https://${shop}/account/login?return_url=${encodeURIComponent('/apps/dashboard')}`
      };
    }
  } catch (error) {
    console.log("Error in loader:", error);
  }
  return {
    shop,
    loggedInCustomerId,
    pathPrefix,
    timestamp,
    signature,
    customer: null,
    locationId,
    shopifyLoginUrl: `https://${shop}/account/login?return_url=${encodeURIComponent('/apps/dashboard')}`
  };
};

export default function DashboardPage() {
  const loaderData = useLoaderData<typeof loader>();
  const { shop, customer, locationId, ...restData } = loaderData;

  console.log("shop", shop)
  console.log("customerrr", JSON.stringify(customer))
  console.log("restData", restData)

  return (
    <LocationProvider locationId={locationId}>
      <SecureDashboard />
    </LocationProvider>
  );
}
