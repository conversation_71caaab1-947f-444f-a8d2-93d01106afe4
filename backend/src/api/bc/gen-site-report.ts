import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAd<PERSON>, checkA<PERSON>en, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";
import * as XLSX from 'xlsx';
import { transformSalesInvoiceHeaders, transformSalesInvoiceLines } from './utils/fieldMapping';
import * as moment from 'moment';
import * as fs from 'fs';

const DEBUG = false

class GenSiteReport implements TypeAPIHandler {

    url = "/api/bc/gen-site-report";
    method = "POST";
    apiSchema = {
        body: Joi.object({
            // clientName: Joi.string(),
            client: Joi.string(),
            postingDateFrom: Joi.string().required(),
            postingDateTo: Joi.string().required(),
            filterLocations: Joi.array().items(Joi.string()).optional(),
        }),
    }

    preHandler = combineMiddlewares([
        validateRequest(this.apiSchema),
    ]);

    handler = async (request: TRequestUser, reply) => {
        const headers = request.headers;
        if (headers['x-api-key'] !== 'kgmdqtveva') {
            return reply.status(401).send({
                success: false,
                message: 'Unauthorized',
            });
        }

        const { client, postingDateFrom, postingDateTo, filterLocations } = request.body;
        
        let saleHeaders = await DB.BCSaleInvoiceHeader.findAll({
            where: {
                BilltoCustomerNo: client,
                PostingDate: {
                    [Op.between]: [postingDateFrom, postingDateTo],
                },
                ...(filterLocations && filterLocations.length > 0 ? {
                    SelltoCustomerNo: {
                        [Op.in]: filterLocations,
                    },
                } : {}),
            },
            raw: true,
            logging: console.log,
        });
        DEBUG && fs.writeFileSync(__dirname + '/saleHeaders.json', JSON.stringify(saleHeaders, null, 2));

        const saleLines = await DB.BCSaleInvoiceLine.findAll({
            where: {
                DocumentNo: {
                    [Op.in]: saleHeaders.map(header => header.No),
                },
            },
            raw: true,
        });
        DEBUG && fs.writeFileSync(__dirname + '/saleLines.json', JSON.stringify(saleLines, null, 2));

        const saleInvoiceHeader = transformSalesInvoiceHeaders(saleHeaders);
        DEBUG && fs.writeFileSync(__dirname + '/saleInvoiceHeader.json', JSON.stringify(saleInvoiceHeader, null, 2));
        const saleInvoiceLine = transformSalesInvoiceLines(saleLines);
        DEBUG && fs.writeFileSync(__dirname + '/saleInvoiceLine.json', JSON.stringify(saleInvoiceLine, null, 2));
        const contractLines = require('./utils/contract_line.json');

        const wb = XLSX.utils.book_new();

        const wsData = [];
        wsData.push([
            'SITE',
            'STOCK DESC',
            'UNITS',
            'QTY',
            'NET'
        ]);
        const indexes = {
            SITE: 0,
            'STOCK DESC': 1,
            UNITS: 2,
            QTY: 3,
            NET: 4,
        };


        const sites = {};

        await BCHelper.getCustomers();

        saleInvoiceHeader.forEach(header => {
            const sellToCustomerNo = header['Sell-to Customer No.'];
            const billToCustomerNo = header['Bill-to Customer No.'];
            // console.log('sellToCustomerNo', sellToCustomerNo);
            // const site = header['Ship-to Name'];
            let site = BCHelper.customers.find(c => c.number === sellToCustomerNo)?.displayName;
            if (!site) {
                site = header['Ship-to Name'];
            }
            const items = saleInvoiceLine.filter(line => line['Document No.'] === header['No.']);
            items.forEach(item => {
                if (!sites[site]) {
                    sites[site] = {
                        name: site,
                        products: [],
                        sellToCustomerNo: sellToCustomerNo,
                    };
                }
                const findContractLine = contractLines.find(cl => cl['No.'] === item['No.'] && cl['Bill-to Customer'] === billToCustomerNo);
                if (!findContractLine) return;
                if (findContractLine['Item Type'] !== 'Consumable') return;

                let findProductIndex = sites[site].products.findIndex(p => p.stockDesc === item['Description']);
                if (findProductIndex === -1) {
                    findProductIndex = sites[site].products.length;
                    sites[site].products[findProductIndex] = {
                        no: item['No.'],
                        stockDesc: item['Description'],
                        units: findContractLine['Unit Of Measure'],
                        qty: +item['Quantity'],
                        net: +item['Line Amount'],
                    };
                } else {
                    sites[site].products[findProductIndex].qty += +item['Quantity'];
                    sites[site].products[findProductIndex].net += +item['Line Amount'];
                }
            });
        });
        Object.keys(sites).forEach(site => {
            // const row = [];
            // row[indexes.SITE] = site;
            // row[indexes['STOCK DESC']] = sites[site].stockDesc;
            // row[indexes.UNITS] = sites[site].units;
            // row[indexes.QTY] = sites[site].qty;
            // row[indexes.NET] = sites[site].net;
            // wsData.push(row);
            wsData.push([site, '', '', '', '']);
            sites[site].products.forEach(product => {
                if (!product.qty) return;
                wsData.push([product.no, product.stockDesc, product.units, product.qty, product.net]);
            });
            wsData.push([]);
        });


        const ws = XLSX.utils.aoa_to_sheet(wsData);
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');


        // send file to client
        const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
        reply.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        reply.header('Content-Disposition', `attachment; filename=site-report-${client}-${postingDateFrom}-${postingDateTo}.xlsx`);
        reply.send(buffer);
    };
}

export default new GenSiteReport();
