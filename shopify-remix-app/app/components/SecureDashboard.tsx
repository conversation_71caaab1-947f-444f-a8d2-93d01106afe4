import { useEffect, useState } from 'react';
import { useLoaderData } from '@remix-run/react';
import Dashboard from './Dashboard';
import type { ICustomer } from 'app/types/shopify-data';

interface SecureSessionData {
  loggedInCustomerId: string;
  shop: string;
  timestamp: string;
  signature: string;
  pathPrefix?: string;
  locationId?: string;
  expiresAt: number;
  signatureVerified?: boolean; // Track if signature was verified
}

interface LoaderData {
  shop: string;
  loggedInCustomerId?: string;
  pathPrefix?: string;
  timestamp?: string;
  signature?: string;
  customer?: ICustomer;
  locationId?: string;
  shopifyLoginUrl: string;
}

const STORAGE_KEY = 'shopify_dashboard_session';
const SESSION_DURATION = 30 * 60 * 1000;

export default function SecureDashboard() {
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const loaderData = useLoaderData<LoaderData>();
  const [sessionData, setSessionData] = useState<SecureSessionData | null>(null);
  const [customerData, setCustomerData] = useState<ICustomer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCustomerData = async (customerId: string, shop: string) => {
    try {
      const resp = await fetch(`/api/customer/${customerId}`);
      const data = await resp.json();
      if (data.success && data.customer) {
        setCustomerData(data.customer as ICustomer);
      } else {
        console.error('API error:', data.error);
        setError(data.error || 'Failed to fetch customer data');
      }
    } catch (error) {
      console.error('Error fetching customer data:', error);
      setError('Failed to fetch customer data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const handleSession = () => {
      if (loaderData.loggedInCustomerId && loaderData.timestamp && loaderData.signature) {

        const newSessionData: SecureSessionData = {
          loggedInCustomerId: loaderData.loggedInCustomerId,
          shop: loaderData.shop,
          timestamp: loaderData.timestamp,
          signature: loaderData.signature,
          pathPrefix: loaderData.pathPrefix,
          locationId: loaderData.locationId,
          expiresAt: Date.now() + SESSION_DURATION,
          signatureVerified: true // Assume verified if we reach this point
        };

        localStorage.setItem(STORAGE_KEY, JSON.stringify(newSessionData));
        setSessionData(newSessionData);

        setCustomerData(loaderData.customer || null);

        // Clean URL to remove sensitive parameters
        const cleanUrl = new URL(window.location.href);
        cleanUrl.search = '';
        window.history.replaceState({}, '', cleanUrl.toString());

        setIsLoading(false);
        return;
      }

      const storedSession = localStorage.getItem(STORAGE_KEY);
      
      if (storedSession) {
        try {
          const parsedSession: SecureSessionData = JSON.parse(storedSession);
          
          if (Date.now() > parsedSession.expiresAt) {
            localStorage.removeItem(STORAGE_KEY);
            setError('Session expired. Please log in again.');
            setIsLoading(false);
            return;
          }
          setSessionData(parsedSession);
          if (!loaderData.customer && parsedSession.loggedInCustomerId) {
            fetchCustomerData(parsedSession.loggedInCustomerId, parsedSession.shop);
          } else {
            setCustomerData(loaderData.customer || null);
            setIsLoading(false);
          }
          return;
        } catch (e) {
          console.error('Error parsing stored session:', e);
          localStorage.removeItem(STORAGE_KEY);
        }
      }

      console.log('No valid session, redirecting to Shopify login');
      setError('No valid session found. Redirecting to login...');
      
      setTimeout(() => {
        window.location.href = loaderData.shopifyLoginUrl;
      }, 3000);
      
      setIsLoading(false);
    };

    handleSession();
  }, [loaderData]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen flex-col">
        <div className="w-10 h-10 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin mb-5"></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-screen flex-col text-center p-5">
        <h2 className="text-red-600 mb-5 text-2xl font-semibold">Access Denied</h2>
        <p className="mb-8 max-w-lg">{error}</p>
        
        <div className="flex gap-4 flex-wrap justify-center">
          <a 
            href={loaderData.shopifyLoginUrl}
            className="bg-blue-600 text-white px-8 py-4 text-decoration-none rounded font-bold hover:bg-blue-700 transition-colors"
          >
            Go to Shopify Login
          </a>
          
          <button
            onClick={() => {
              localStorage.removeItem(STORAGE_KEY);
              window.location.reload();
            }}
            className="bg-gray-500 text-white px-8 py-4 border-none rounded font-bold cursor-pointer hover:bg-gray-600 transition-colors"
          >
            Clear Session & Retry
          </button>
        </div>
        
        <p className="mt-5 text-sm text-gray-600">
          If you opened this link in a new tab or it was shared with you, 
          please access the dashboard through your Shopify store.
        </p>
      </div>
    );
  }

  if (sessionData) {
    return <Dashboard customer={customerData || undefined} />;
  }

  return null;
}
