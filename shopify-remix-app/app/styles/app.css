@import "tailwindcss";
@import "./admin-dashboard.css";

@theme {
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;
  
  --color-msc-teal: #00a8a8;
  --color-msc-navy: #003057;
  --color-msc-grey: #f7f7f7;
  --color-msc-darkgrey: #6b7280;
  
  --font-family-sans: "Inter", sans-serif;
}

@layer base {
  body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  }
}

@layer components {
  .kpi-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .product-item {
    @apply flex items-center justify-between p-3 bg-gray-50 rounded-lg;
  }
  
  .status-badge {
    @apply px-2 inline-flex text-xs leading-5 font-semibold rounded-full;
  }
  
  .status-delivered {
    @apply bg-green-100 text-green-800;
  }
  
  .status-shipped {
    @apply bg-blue-100 text-blue-800;
  }
  
  .status-processing {
    @apply bg-gray-100 text-gray-800;
  }
}

[data-fa-i2svg] {
  width: 16px;
  height: 16px;
}

button {
  cursor: pointer;
}