import React, { useState, useCallback, useMemo, useEffect } from "react";
import {
  Button,
  Modal,
  TextField,
  BlockStack,
  InlineStack,
  Text,
  Card,
  Icon,
  Badge
} from "@shopify/polaris";
import { SearchIcon, LocationIcon } from "@shopify/polaris-icons";
import { RoleAssignmentItem } from "app/types/shopify-data";
import { useCustomerLocationSearch } from "app/hooks/useCustomerActions";

interface Props {
  locations: RoleAssignmentItem[];
  selectedLocation?: RoleAssignmentItem | null;
  onLocationSelect: (location: RoleAssignmentItem) => void;
  placeholder?: string;
  label?: string;
  customerId?: string;
}

export const CustomerLocationSelector: React.FC<Props> = ({
  locations,
  selectedLocation,
  onLocationSelect,
  placeholder = "Select a location",
  label = "Location",
  customerId
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const { searchLocations, loading: searchLoading, data: searchData } = useCustomerLocationSearch();

  const handleModalClose = useCallback(() => {
    setIsModalOpen(false);
    setSearchTerm("");
  }, []);

  const handleLocationSelect = useCallback((location: RoleAssignmentItem | null) => {
    onLocationSelect(location!);
    handleModalClose();
  }, [onLocationSelect, handleModalClose]);

  const handleAllLocationSelect = useCallback(() => {
    onLocationSelect(null as any);
    handleModalClose();
  }, [onLocationSelect, handleModalClose]);

  const filteredLocations = useMemo(() => {
    if (searchTerm.trim() && searchData?.locations) {
      return searchData.locations;
    }

    if (!searchTerm.trim()) {
      return locations;
    }

    const searchLower = searchTerm.toLowerCase();
    return locations.filter(location => {
      const name = location.companyLocation.name.toLowerCase();
      const externalId = location.companyLocation.externalId?.toLowerCase() || '';

      return name.includes(searchLower) ||
        externalId.includes(searchLower);
    });
  }, [locations, searchTerm, searchData?.locations]);

  useEffect(() => {
    if (searchTerm.trim() && customerId) {
      const debounceTimeout = setTimeout(() => {
        searchLocations(customerId, searchTerm, 50);
      }, 500);

      return () => clearTimeout(debounceTimeout);
    }
  }, [searchTerm, customerId]);

  const modalMarkup = (
    <Modal
      open={isModalOpen}
      onClose={handleModalClose}
      title={`Select ${label}`}
      primaryAction={{
        content: "Cancel",
        onAction: handleModalClose,
      }}
      secondaryActions={[]}
    >
      <Modal.Section>
        <BlockStack gap="400">
          <TextField
            label=""
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder={`Search ${label.toLowerCase()}s...`}
            prefix={<Icon source={SearchIcon} />}
            autoComplete="off"
            disabled={searchLoading}
          />

          <div style={{ maxHeight: "400px", overflowY: "auto" }}>
            <BlockStack gap="200">
              {/* All Location Option */}
              <div
                onClick={handleAllLocationSelect}
                style={{ cursor: "pointer" }}
              >
                <Card padding="300">
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text as="h3" variant="bodyMd" fontWeight="semibold">
                        All Location
                      </Text>
                      {!selectedLocation && (
                        <Badge tone="success">Selected</Badge>
                      )}
                    </InlineStack>
                  </BlockStack>
                </Card>
              </div>

              {/* Search Results Summary */}
              {searchTerm && (
                <Text as="p" variant="bodySm" tone="subdued">
                  {searchLoading ? (
                    "Searching..."
                  ) : searchData?.error ? (
                    `Error: ${searchData.error}`
                  ) : (
                    `Found ${filteredLocations.length} location${filteredLocations.length !== 1 ? 's' : ''} matching "${searchTerm}"`
                  )}
                </Text>
              )}

              {filteredLocations.length === 0 ? (
                <Card>
                  <BlockStack gap="200" align="center">
                    <Icon source={LocationIcon} tone="subdued" />
                    <Text as="p" tone="subdued">
                      {searchTerm ? "No locations found matching your search." : "No locations available."}
                    </Text>
                  </BlockStack>
                </Card>
              ) : (
                filteredLocations.map((location) => (
                  <div
                    key={location.id}
                    onClick={() => handleLocationSelect(location)}
                    style={{ cursor: "pointer" }}
                  >
                    <Card padding="300">
                      <BlockStack gap="200">
                        <InlineStack align="space-between">
                          <Text as="h3" variant="bodyMd" fontWeight="semibold">
                            {location.companyLocation?.name}
                          </Text>
                          {selectedLocation?.id === location.id && (
                            <Badge tone="success">Selected</Badge>
                          )}
                        </InlineStack>
                        {location?.companyLocation?.externalId && (
                          <Text as="p" variant="bodySm" tone="subdued">
                            {location.companyLocation.externalId}
                          </Text>
                        )}
                      </BlockStack>
                    </Card>
                  </div>
                ))
              )}
            </BlockStack>
          </div>
        </BlockStack>
      </Modal.Section>
    </Modal>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <BlockStack gap="200">
        <Text as="h3" variant="bodyMd" fontWeight="semibold">
          Select Location
        </Text>
        <Button
          onClick={() => setIsModalOpen(true)}
          variant="tertiary"
          textAlign="left"
          fullWidth
          icon={LocationIcon}
        >
          {selectedLocation ? selectedLocation?.companyLocation?.name : "All Location"}
        </Button>
        {modalMarkup}
      </BlockStack>
    </div>
  );
};
